/**
 * 相册管理页面样式
 *
 * 设计说明：
 * 采用现代化的网格布局，参考course-edit页面的图片管理设计
 * 优先使用TDesign组件，保持设计一致性
 */

/**
 * 页面容器样式
 */
.album-management-container {
  min-height: 100vh;
  padding: 0;
  background-color: #f8f9fa;
  box-sizing: border-box;
}

/**
 * 底部导航栏样式
 *
 * 设计说明：
 * Android风格的底部导航栏
 * 包括最近、收藏、文件夹、全部照片等分类
 */
.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);
}

/**
 * 导航标签容器
 */
.nav-tabs {
  display: flex;
  padding: 0;
  background: #fff;
}

/**
 * 单个导航标签样式
 */
.nav-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx 8rpx;
  gap: 6rpx;
  color: #999;
  font-size: 20rpx;
  transition: all 0.3s ease;
  position: relative;
  min-height: 100rpx;
}

/**
 * 激活状态的导航标签
 */
.nav-tab.active {
  color: #0052d9;
}

/**
 * 导航标签文字
 */
.nav-text {
  font-size: 20rpx;
  line-height: 1.2;
}

/**
 * 页面右上角菜单按钮
 *
 * 设计说明：
 * Android风格的三点菜单按钮
 * 隐藏管理功能，保持界面简洁
 */
.page-menu {
  position: fixed;
  top: 24rpx;
  right: 32rpx;
  z-index: 200;
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

/**
 * 菜单按钮点击效果
 */
.page-menu:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.8);
}

/**
 * 相册内容区域样式
 *
 * 设计说明：
 * 为底部导航栏留出空间
 * 包含不同分类的相册展示区域
 */
.album-content {
  padding: 24rpx 32rpx 140rpx; /* 为底部导航栏留出空间 */
  min-height: calc(100vh - 140rpx);
}

/**
 * 相册卡片样式
 *
 * 设计说明：
 * 模仿Android相册应用的相册卡片设计
 * 每个卡片显示封面图和相册信息
 */
.album-card {
  margin-bottom: 24rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

/**
 * 相册卡片点击效果
 */
.album-card:active {
  transform: scale(0.98);
}

/**
 * 相册封面容器
 */
.album-cover {
  position: relative;
  width: 100%;
  height: 300rpx;
}

/**
 * 封面图片样式
 */
.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/**
 * 相册信息覆盖层
 */
.album-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 40rpx 24rpx 24rpx;
  color: #fff;
}

/**
 * 相册名称样式
 */
.album-name {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

/**
 * 相册图片数量样式
 */
.album-count {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
}

/**
 * 收藏标识样式
 */
.favorite-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/**
 * 页面标题样式
 */
.page-title {
  padding: 32rpx 32rpx 16rpx;
  background: #fff;
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

/**
 * 文件夹视图样式
 *
 * 设计说明：
 * Android风格的文件夹展示界面
 * 类似原生文件管理器的文件夹布局
 */
.folder-view {
  width: 100%;
}

/**
 * 文件夹列表容器
 */
.folder-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  padding: 0;
}

/**
 * 文件夹卡片样式
 *
 * 设计说明：
 * 每个文件夹显示为卡片形式
 * 包含封面图、名称和照片数量
 */
.folder-card {
  border-radius: 16rpx;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

/**
 * 文件夹卡片点击效果
 */
.folder-card:active {
  transform: scale(0.98);
}

/**
 * 文件夹封面容器
 */
.folder-cover {
  position: relative;
  width: 100%;
  height: 240rpx;
}

/**
 * 空文件夹样式
 */
.empty-folder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

/**
 * 文件夹信息覆盖层
 */
.folder-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 40rpx 24rpx 24rpx;
  color: #fff;
}

/**
 * 文件夹名称样式
 */
.folder-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 6rpx;
}

/**
 * 文件夹照片数量样式
 */
.folder-count {
  display: block;
  font-size: 22rpx;
  opacity: 0.9;
}

/**
 * 默认文件夹标识
 */
.default-badge {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/**
 * 文件夹选择器样式
 *
 * 设计说明：
 * 底部弹出的文件夹选择界面
 * 用于移动和复制操作
 */
.folder-selector {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
}

/**
 * 选择器头部
 */
.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/**
 * 选择器标题
 */
.selector-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/**
 * 选择器内容区域
 */
.selector-content {
  padding: 24rpx 0;
  max-height: 60vh;
  overflow-y: auto;
}

/**
 * 文件夹选项样式
 */
.folder-option {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  gap: 24rpx;
  transition: background-color 0.2s ease;
}

/**
 * 文件夹选项点击效果
 */
.folder-option:active {
  background-color: #f5f5f5;
}

/**
 * 文件夹选项名称
 */
.folder-option-name {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/**
 * 新建文件夹选项特殊样式
 */
.folder-option-name.new-folder {
  color: #0052d9;
  font-weight: 500;
}

/**
 * 文件夹选项数量
 */
.folder-option-count {
  font-size: 24rpx;
  color: #999;
}

/**
 * Android风格选择模式顶部栏
 *
 * 设计说明：
 * 模仿Android相册的选择模式顶部栏
 * 显示选中数量和关闭按钮
 */
.selection-header {
  position: fixed;
  top: 0; /* 现在直接在顶部 */
  left: 0;
  right: 0;
  z-index: 150;
  background: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/**
 * 选择信息容器
 */
.selection-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

/**
 * 选择数量文字
 */
.selection-count {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/**
 * Android风格圆形选择框
 *
 * 设计说明：
 * 模仿Android相册的圆形选择框
 * 未选中时为空心圆，选中时为实心圆带勾
 */
.selection-circle {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 4rpx solid #fff;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  transition: all 0.2s ease;
}

/**
 * 选中状态的选择框
 */
.selection-circle.selected {
  background: #0052d9;
  border-color: #0052d9;
}

/**
 * 底部操作栏 - Android风格
 *
 * 设计说明：
 * 模仿Android相册的底部操作栏
 * 固定在屏幕底部，包含删除、分享等操作
 */
.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 150;
  background: #fff;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.06);
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #f0f0f0;
}

/**
 * 操作栏内容容器
 */
.action-bar-content {
  display: flex;
  justify-content: center;
}

/**
 * 操作按钮组
 */
.action-buttons {
  display: flex;
  gap: 80rpx;
  align-items: center;
}

/**
 * 单个操作按钮
 */
.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx;
  border-radius: 12rpx;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

/**
 * 操作按钮点击效果
 */
.action-button:active {
  background-color: #f5f5f5;
}

/**
 * 操作按钮文字
 */
.action-text {
  font-size: 24rpx;
  color: #666;
}

/**
 * 普通操作按钮区域样式
 *
 * 设计说明：
 * 只在非选择模式下显示
 * 调整位置以适应新的布局
 */
.album-header {
  position: fixed;
  top: 0; /* 现在直接在顶部 */
  left: 32rpx;
  right: 32rpx;
  z-index: 100;
  background: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  padding: 24rpx 0 16rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  overflow-x: auto;
  box-sizing: border-box;
  border-radius: 0 0 12rpx 12rpx;
}

/**
 * 操作按钮样式
 *
 * 设计说明：
 * 按照TDesign设计规范设置按钮样式
 * 确保按钮宽度足够显示完整文字
 */
.action-btn {
  border-radius: 6rpx !important;
  font-weight: 400 !important;
  box-shadow: none !important;
  padding: 0 24rpx !important;
  min-width: 160rpx;
  max-width: 200rpx;
  white-space: nowrap;
  flex-shrink: 0;
  font-size: 26rpx !important;
  height: 64rpx !important;
}

/**
 * 默认按钮样式（选择首页图片）
 */
.action-btn[theme="default"] {
  background: #f3f3f3 !important;
  color: #666 !important;
  border: 1rpx solid #d9d9d9 !important;
}

/**
 * 主要按钮样式（完成选择）
 */
.action-btn[theme="primary"] {
  background: #0052d9 !important;
  color: #fff !important;
  border: 1rpx solid #0052d9 !important;
}

/**
 * 危险按钮样式（删除模式）
 */
.action-btn[theme="danger"] {
  background: #e34d59 !important;
  color: #fff !important;
  border: 1rpx solid #e34d59 !important;
}

/**
 * 全部相册容器样式
 *
 * 设计说明：
 * 在全部相册模式下，需要为操作按钮留出额外空间
 */
.all-albums {
  margin-top: 80rpx; /* 为操作按钮留出空间 */
}

/**
 * 图片网格容器样式
 *
 * 设计说明：
 * 使用CSS Grid布局创建响应式的图片网格
 * 参考course-edit页面的image-grid样式
 * 调整顶部边距以适应新布局
 */
.album-grid {
  margin-top: 0; /* 移除原有的顶部边距，由父容器控制 */
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  width: 100%;
}

/**
 * 图片项容器样式
 *
 * 设计说明：
 * 每个图片项的容器，包含图片和各种操作按钮
 * 使用相对定位为子元素提供定位基准
 */
.album-item {
  position: relative;
  width: 100%;
  aspect-ratio: 1; /* 保持1:1的宽高比 */
  border-radius: 8rpx;
  overflow: hidden;
  background-color: #f5f5f5;
}

/**
 * 相册图片样式
 */
.album-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

/**
 * 上传按钮项样式
 *
 * 设计说明：
 * 参考course-edit页面的image-add-btn样式
 * 使用虚线边框和柔和的颜色
 */
.upload-item {
  border: 2rpx dashed #ddd;
  background-color: #fafafa;
  display: flex;
  align-items: center;
  justify-content: center;
}

/**
 * 上传按钮容器样式
 */
.upload-btn-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

/**
 * 上传按钮文字样式
 */
.upload-text {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/**
 * 上传状态提示样式
 */
.upload-status {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
}

/**
 * 上传中文字样式
 */
.uploading-text {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #666;
}



/**
 * 首页编号徽章样式
 *
 * 设计说明：
 * 显示在图片左上角的编号徽章
 * 使用品牌绿色，突出显示首页图片
 */
.banner-order-badge {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  z-index: 15;
  background: #52c41a;
  color: white;
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

/**
 * 删除模式复选框样式
 */
.delete-checkbox {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  z-index: 15;
  transform: scale(1.2);
}

.album-management-container {
  min-height: 100vh; /* 保证内容区至少和屏幕一样高 */
  height: auto;      /* 允许内容自动撑开 */
  overflow: visible; /* 保证内容不会被裁剪 */
  padding-left: 32rpx;
  padding-right: 32rpx;
  padding-top: 0;
  padding-bottom: 0;
  box-sizing: border-box;
} 

/**
 * 警告提示样式
 *
 * 设计说明：
 * 简洁的文字提示，无背景
 * 图标和文字在同一行显示
 */
.album-warning {
  margin-top: 40rpx;
  padding: 24rpx 0;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  text-align: center;
}

/**
 * 高亮文字样式
 */
.album-warning .highlight {
  color: #fa541c;
  font-weight: 600;
}

.delete-checkbox {
  /* 让复选框绝对定位在图片右上角，类似HTML的position: absolute; */
  position: absolute;
  right: 12rpx; /* 距离右边12rpx */
  top: 12rpx;  /* 距离顶部12rpx */
  z-index: 10; /* 保证在图片之上 */
  /* 缩小复选框，提升美观。类似CSS的transform: scale(1.1); */
  transform: scale(1.1);
  /* 可选：为兼容小程序不同机型，建议加-webkit-transform */
  -webkit-transform: scale(1.1);
}

/**
 * 加载状态项样式
 */
.loading-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  color: #999;
}

/**
 * 加载文字样式
 */
.loading-text {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #999;
}

/**
 * 结束提示项样式
 *
 * 设计说明：
 * 占用一张图片的位置，与其他图片保持一致的布局
 */
.end-item {
  background-color: #fafafa;
  border: 1rpx solid #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: center;
}

/**
 * 结束提示内容样式
 */
.end-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

/**
 * 结束提示文字样式
 */
.end-text {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #999;
}

/**
 * 响应式设计
 */
@media screen and (max-width: 750rpx) {
  /* 分类标签区域响应式 */
  .album-categories {
    padding: 20rpx 24rpx 12rpx;
  }

  .category-tabs {
    gap: 16rpx;
  }

  .category-tab {
    padding: 12rpx 16rpx;
    gap: 6rpx;
  }

  .category-tab text {
    font-size: 22rpx;
  }

  /* 相册内容区域响应式 */
  .album-content {
    margin-top: 100rpx;
    padding: 20rpx 24rpx;
  }

  /* 相册卡片响应式 */
  .album-card {
    margin-bottom: 20rpx;
    border-radius: 12rpx;
  }

  .album-cover {
    height: 250rpx;
  }

  .album-info {
    padding: 32rpx 20rpx 20rpx;
  }

  .album-name {
    font-size: 28rpx;
    margin-bottom: 6rpx;
  }

  .album-count {
    font-size: 22rpx;
  }

  .favorite-badge {
    width: 40rpx;
    height: 40rpx;
    top: 12rpx;
    right: 12rpx;
  }

  /* Android风格选择模式响应式 */
  .selection-header {
    top: 0;
    padding: 20rpx 24rpx;
  }

  .selection-count {
    font-size: 28rpx;
  }

  .selection-circle {
    width: 40rpx;
    height: 40rpx;
    top: 10rpx;
    right: 10rpx;
    border: 3rpx solid #fff;
  }

  .bottom-action-bar {
    padding: 20rpx 24rpx;
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  }

  .action-buttons {
    gap: 60rpx;
  }

  .action-button {
    padding: 12rpx;
    gap: 6rpx;
  }

  .action-text {
    font-size: 22rpx;
  }

  /* 文件夹视图响应式 */
  .folder-list {
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
  }

  .folder-cover {
    height: 200rpx;
  }

  .folder-info {
    padding: 32rpx 20rpx 20rpx;
  }

  .folder-name {
    font-size: 26rpx;
    margin-bottom: 4rpx;
  }

  .folder-count {
    font-size: 20rpx;
  }

  .default-badge {
    width: 40rpx;
    height: 40rpx;
    top: 12rpx;
    left: 12rpx;
  }

  /* 文件夹选择器响应式 */
  .selector-header {
    padding: 28rpx 24rpx;
  }

  .selector-title {
    font-size: 28rpx;
  }

  .selector-content {
    padding: 20rpx 0;
  }

  .folder-option {
    padding: 20rpx 24rpx;
    gap: 20rpx;
  }

  .folder-option-name {
    font-size: 26rpx;
  }

  .folder-option-count {
    font-size: 22rpx;
  }

  /* 页面菜单按钮响应式 */
  .page-menu {
    top: 20rpx;
    right: 24rpx;
    width: 56rpx;
    height: 56rpx;
  }

  /* 操作按钮区域响应式 */
  .album-header {
    left: 24rpx;
    right: 24rpx;
    gap: 12rpx;
    top: 0;
  }

  .action-btn {
    padding: 0 20rpx !important;
    min-width: 140rpx;
    max-width: 180rpx;
    font-size: 24rpx !important;
    height: 60rpx !important;
  }

  /* 全部相册区域响应式 */
  .all-albums {
    margin-top: 70rpx;
  }

  .album-grid {
    gap: 12rpx;
  }

  .upload-text {
    font-size: 22rpx;
  }

  .loading-text {
    font-size: 22rpx;
  }

  .end-text {
    font-size: 22rpx;
  }

  .album-warning {
    font-size: 24rpx;
    padding: 20rpx 0;
  }
}

/* ==================== Android风格图片预览弹窗样式 ==================== */

.image-preview-popup {
  width: 100vw;
  height: 100vh;
  background-color: #000;
  position: relative;
  overflow: hidden;
}

/* 预览顶部状态栏 */
.preview-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background: linear-gradient(180deg, rgba(0,0,0,0.6) 0%, transparent 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  padding-top: var(--status-bar-height, 44rpx);
  z-index: 1000;
  transition: opacity 0.3s ease;
}

.preview-header.visible {
  opacity: 1;
}

.preview-header.hidden {
  opacity: 0;
  pointer-events: none;
}

.header-left,
.header-right {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(0,0,0,0.3);
}

.header-center {
  flex: 1;
  text-align: center;
}

.image-counter {
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
}

/* 预览图片轮播区域 */
.preview-swiper {
  width: 100%;
  height: 100%;
}

.preview-swiper-item {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  background-color: transparent;
}

/* 预览底部操作栏 */
.preview-bottom-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(0deg, rgba(0,0,0,0.8) 0%, transparent 100%);
  padding: 40rpx 32rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
  z-index: 1000;
  transition: opacity 0.3s ease;
}

.preview-bottom-bar.visible {
  opacity: 1;
}

.preview-bottom-bar.hidden {
  opacity: 0;
  pointer-events: none;
}

.preview-actions {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.preview-action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  border-radius: 16rpx;
  min-width: 120rpx;
  transition: background-color 0.2s ease;
}

.preview-action-item:active {
  background-color: rgba(255,255,255,0.1);
}

.preview-action-label {
  color: #fff;
  font-size: 24rpx;
  margin-top: 8rpx;
  text-align: center;
}

/* 预览加载遮罩 */
.preview-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .preview-actions {
    justify-content: space-between;
    padding: 0 20rpx;
  }

  .preview-action-item {
    min-width: 100rpx;
    padding: 12rpx;
  }

  .preview-action-label {
    font-size: 22rpx;
  }
}

