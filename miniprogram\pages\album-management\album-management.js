// 获取全局 app 实例（如需用到全局数据可用）
const app = getApp();

// 引入云开发能力
const db = wx.cloud.database(); // 获取数据库实例
import { showToast, showLoading, hideToast, showError } from '../../utils/toast.js';

Page({
  data: {
    // 当前选中的分类：'favorites'(收藏)、'folders'(文件夹)、'all'(全部照片)
    currentCategory: 'favorites',

    // 原有的相册图片数据
    albumImages: [], // 存储相册图片列表（每项包含 _id、fileID、tempFileURL、bannerOrder、selected）
    maxBannerCount: 5, // 首页最多展示5张图片
    chooseBannerMode: false, // 是否激活首页图片选择模式
    deleteMode: false, // 是否激活批量删除模式
    favoriteMode: false, // 是否激活批量收藏模式
    page: 0, // 当前页码，从0开始
    pageSize: 20, // 每页加载20张图片
    hasMore: true, // 是否还有更多图片可加载
    loading: false, // 是否正在加载中
    isUploading: false, // 是否正在上传图片

    // 新增的相册分类数据
    favoriteAlbums: [], // 收藏相册列表
    albumFolders: [], // 所有文件夹/相册列表

    // Android风格选择模式数据
    selectedCount: 0, // 选中的图片数量
    bannerSelectedCount: 0, // 选中的首页图片数量

    // 文件夹管理相关数据
    currentFolder: null, // 当前查看的文件夹
    showFolderManagement: false, // 是否显示文件夹管理界面
    moveMode: false, // 是否处于移动模式
    copyMode: false, // 是否处于复制模式
    showFolderSelector: false, // 是否显示文件夹选择器
    targetFolderId: '',       // 目标文件夹ID
    folders: [],              // 文件夹列表

    // Android风格图片预览相关数据
    showImagePreview: false,  // 是否显示图片预览弹窗
    previewCurrentIndex: 0,   // 预览当前图片索引
    previewCurrentImage: {},  // 预览当前图片对象
    showPreviewControls: true, // 是否显示预览控制栏
    previewLoading: false,    // 预览是否正在加载
    overlayProps: {           // 弹窗遮罩属性
      backgroundColor: 'rgba(0,0,0,0.9)'
    }
  },

  // 页面加载时初始化数据
  onLoad: function() {
    this.initAlbumData();
  },

  /**
   * 初始化相册数据
   *
   * 功能说明：
   * 1. 加载全部图片数据
   * 2. 生成最近相册和收藏相册的模拟数据
   * 3. 设置默认显示最近相册
   */
  initAlbumData: function() {
    // 加载全部图片
    this.loadAlbumImages(true);

    // 生成模拟的相册分类数据
    this.generateAlbumCategories();
  },

  /**
   * 生成相册分类数据
   *
   * 设计说明：
   * 模拟Android相册应用的分类展示
   * 包括最近相册和收藏相册，以及加载文件夹数据
   */
  generateAlbumCategories: function() {
    // 加载文件夹数据
    this.loadAlbumFolders();

    // 加载收藏图片
    this.loadFavoriteImages();
  },

  /**
   * 加载收藏图片
   */
  loadFavoriteImages: function() {
    const db = wx.cloud.database();

    // 查询收藏分类的图片
    db.collection('album_images')
      .where({
        category: 'favorite'
      })
      .orderBy('createTime', 'desc')
      .get()
      .then(res => {
        if (res.data.length > 0) {
          // 获取临时访问链接
          const fileList = res.data.map(item => item.fileID);
          wx.cloud.getTempFileURL({
            fileList,
            success: urlRes => {
              const urlMap = {};
              urlRes.fileList.forEach(file => {
                urlMap[file.fileID] = file.tempFileURL;
              });

              // 组装收藏相册数据
              const favoriteAlbums = [{
                id: 'favorites',
                name: '我的收藏',
                count: res.data.length,
                coverImage: urlMap[res.data[0].fileID] || '',
                images: res.data.map(item => ({
                  ...item,
                  tempFileURL: urlMap[item.fileID] || ''
                }))
              }];

              this.setData({ favoriteAlbums });
            },
            fail: () => {
              this.setData({ favoriteAlbums: [] });
            }
          });
        } else {
          this.setData({ favoriteAlbums: [] });
        }
      })
      .catch(() => {
        this.setData({ favoriteAlbums: [] });
      });
  },

  /**
   * 加载文件夹数据
   *
   * 功能说明：
   * 从数据库加载所有相册文件夹
   */
  loadAlbumFolders: function() {
    const db = wx.cloud.database();
    db.collection('album_folders')
      .orderBy('createTime', 'desc')
      .get()
      .then(res => {
        // 为每个文件夹计算图片数量和封面
        const folderPromises = res.data.map(folder => this.getFolderInfo(folder));
        Promise.all(folderPromises).then(folders => {
          this.setData({ albumFolders: folders });
        });
      })
      .catch(err => {
        console.error('加载文件夹失败:', err);
        // 如果没有文件夹集合，创建默认文件夹
        this.createDefaultFolders();
      });
  },

  /**
   * 获取文件夹信息（图片数量和封面）
   */
  getFolderInfo: function(folder) {
    return new Promise((resolve) => {
      const db = wx.cloud.database();
      db.collection('album_images')
        .where({ folderId: folder._id })
        .count()
        .then(countRes => {
          // 获取文件夹中的第一张图片作为封面
          db.collection('album_images')
            .where({ folderId: folder._id })
            .orderBy('createTime', 'desc')
            .limit(1)
            .get()
            .then(imageRes => {
              let coverImage = '';
              if (imageRes.data.length > 0) {
                // 获取临时URL
                wx.cloud.getTempFileURL({
                  fileList: [imageRes.data[0].fileID],
                  success: urlRes => {
                    coverImage = urlRes.fileList[0].tempFileURL;
                    resolve({
                      ...folder,
                      count: countRes.total,
                      coverImage
                    });
                  },
                  fail: () => {
                    resolve({
                      ...folder,
                      count: countRes.total,
                      coverImage: ''
                    });
                  }
                });
              } else {
                resolve({
                  ...folder,
                  count: 0,
                  coverImage: ''
                });
              }
            });
        });
    });
  },

  /**
   * 创建默认文件夹
   */
  createDefaultFolders: function() {
    const db = wx.cloud.database();
    const defaultFolders = [
      { name: '默认相册', description: '系统默认相册', isDefault: true },
      { name: '精选照片', description: '精心挑选的照片', isDefault: false }
    ];

    const createPromises = defaultFolders.map(folder =>
      db.collection('album_folders').add({
        data: {
          ...folder,
          createTime: new Date()
        }
      })
    );

    Promise.all(createPromises).then(() => {
      this.loadAlbumFolders();
    });
  },

  /**
   * 分类切换处理函数
   *
   * 功能说明：
   * 切换不同的相册分类视图
   * 重置操作模式状态
   */
  onCategoryChange: function(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      currentCategory: category,
      // 切换分类时重置操作模式
      chooseBannerMode: false,
      deleteMode: false,
      moveMode: false,
      copyMode: false
    });
  },

  /**
   * 相册卡片点击处理函数
   *
   * 功能说明：
   * 点击相册卡片时的处理逻辑
   * 可以扩展为跳转到具体相册详情页
   */
  onAlbumTap: function(e) {
    const album = e.currentTarget.dataset.album;
    console.log('点击相册:', album);

    // 这里可以添加跳转到具体相册的逻辑
    // 例如：wx.navigateTo({ url: `/pages/album-detail/album-detail?id=${album.id}` });

    // 临时处理：显示提示信息
    wx.showToast({
      title: `打开${album.name}`,
      icon: 'none',
      duration: 1500
    });
  },

  /**
   * Android风格长按进入选择模式
   *
   * 功能说明：
   * 长按图片进入删除选择模式，模仿Android相册交互
   * 自动选中当前长按的图片
   */
  onImageLongPress: function(e) {
    // 只在全部相册模式下且非选择模式时响应长按
    if (this.data.currentCategory !== 'all' || this.data.deleteMode || this.data.chooseBannerMode) {
      return;
    }

    const index = e.currentTarget.dataset.index;

    // 进入删除选择模式
    let albumImages = this.data.albumImages.map((img, i) => ({
      ...img,
      selected: i === index // 自动选中长按的图片
    }));

    this.setData({
      deleteMode: true,
      albumImages,
      selectedCount: 1
    });

    // 触觉反馈
    wx.vibrateShort();
  },

  /**
   * 取消选择模式
   *
   * 功能说明：
   * 点击关闭按钮退出选择模式
   */
  onCancelSelectionMode: function() {
    // 清除所有选中状态
    let albumImages = this.data.albumImages.map(img => ({
      ...img,
      selected: false
    }));

    this.setData({
      deleteMode: false,
      chooseBannerMode: false,
      moveMode: false,
      copyMode: false,
      favoriteMode: false,
      albumImages,
      selectedCount: 0,
      bannerSelectedCount: 0,
      showFolderSelector: false
    });
  },

  /**
   * 更新选中数量统计
   *
   * 功能说明：
   * 统计当前选中的图片数量
   */
  updateSelectionCount: function() {
    const selectedCount = this.data.albumImages.filter(img => img.selected).length;
    const bannerSelectedCount = this.data.albumImages.filter(img => img.bannerOrder).length;

    this.setData({
      selectedCount,
      bannerSelectedCount
    });
  },

  // 分页加载图片
  loadAlbumImages: function(reset = false) {
    if (this.data.loading || (!this.data.hasMore && !reset)) return; // 正在加载或没有更多时不再请求
    this.setData({ loading: true });
    let page = reset ? 0 : this.data.page;
    if (reset) {
      this.setData({ albumImages: [], hasMore: true, page: 0 });
    }
    db.collection('album_images')
      .orderBy('createTime', 'desc')
      .skip(page * this.data.pageSize)
      .limit(this.data.pageSize)
      .get()
      .then(res => {
        const fileList = res.data.map(item => item.fileID);
        if (fileList.length === 0) {
          this.setData({ hasMore: false, loading: false });
          return;
        }
        wx.cloud.getTempFileURL({
          fileList: fileList,
          success: urlRes => {
            const urlMap = {};
            urlRes.fileList.forEach(file => {
              urlMap[file.fileID] = file.tempFileURL;
            });
            const newImages = res.data.map(item => ({
              ...item,
              tempFileURL: urlMap[item.fileID] || '',
              bannerOrder: item.bannerOrder || null
            }));
            const albumImages = reset ? newImages : this.data.albumImages.concat(newImages);
            this.setData({
              albumImages,
              hasMore: newImages.length === this.data.pageSize, // 如果本页数量等于pageSize，说明可能还有下一页
              page: page + 1,
              loading: false
            });
          },
          fail: () => {
            this.setData({ loading: false });
          }
        });
      });
  },

  // 页面滚动到底部时自动加载下一页
  onReachBottom: function() {
    this.loadAlbumImages();
  },

  // 获取当前用户的相册图片列表
  getAlbumImages: function() {
    db.collection('album_images')
      .orderBy('createTime', 'desc')
      .limit(100) // 明确指定最多拉取100条，防止只显示部分图片
      .get()
      .then(res => {
        const fileList = res.data.map(item => item.fileID);
        if (fileList.length === 0) {
          this.setData({ albumImages: [] });
          return;
        }
        wx.cloud.getTempFileURL({
          fileList: fileList,
          success: urlRes => {
            const urlMap = {};
            urlRes.fileList.forEach(file => {
              urlMap[file.fileID] = file.tempFileURL;
            });
            // 组装 albumImages 数组，带 bannerOrder 字段
            const albumImages = res.data.map(item => ({
              ...item,
              tempFileURL: urlMap[item.fileID] || '',
              bannerOrder: item.bannerOrder || null // 可能为 undefined/null/数字
            }));
            this.setData({ albumImages });
          },
          fail: err => {
            showToast(this, { message: '获取图片失败', theme: 'error' });
          }
        });
      });
  },

  // 激活首页图片选择模式
  onChooseBannerMode: function() {
    this.setData({
      chooseBannerMode: true,
      deleteMode: false,
      favoriteMode: false,
      moveMode: false,
      copyMode: false
    });

    // 重置选择状态，确保界面正确显示
    this.updateSelectionCount();

    showToast(this, { message: '请选择首页展示图片', theme: 'info' });
  },

  // 退出首页图片选择模式
  onCancelChooseBanner: function() {
    this.setData({ chooseBannerMode: false });
  },

  // 激活删除模式
  onDeleteMode: function() {
    // 进入批量删除模式时，为每张图片加selected字段，初始为false
    let albumImages = this.data.albumImages.map(img => ({ ...img, selected: false }));
    this.setData({ deleteMode: true, albumImages });
  },

  // 选择/取消选中图片（选择模式下）
  onSelectImage: function(e) {
    // 获取当前图片索引
    const index = e.currentTarget.dataset.index;
    let albumImages = this.data.albumImages;

    if (this.data.deleteMode) {
      // 删除模式：切换选中状态
      albumImages[index].selected = !albumImages[index].selected;
    } else if (this.data.chooseBannerMode) {
      // 首页图片选择模式：处理首页图片选择逻辑
      this.handleBannerSelection(index);
      return;
    } else if (this.data.favoriteMode) {
      // 收藏模式：切换选中状态
      albumImages[index].selected = !albumImages[index].selected;
    }

    this.setData({ albumImages });
    this.updateSelectionCount();
  },

  /**
   * 处理首页图片选择逻辑
   */
  handleBannerSelection: function(index) {
    let albumImages = this.data.albumImages;
    const img = albumImages[index];

    if (img.bannerOrder) {
      // 如果已经是首页图片，取消选择
      img.bannerOrder = null;
    } else {
      // 检查是否已达到最大数量
      const currentBannerCount = albumImages.filter(item => item.bannerOrder).length;
      if (currentBannerCount >= this.data.maxBannerCount) {
        showToast(this, {
          message: `最多只能选择${this.data.maxBannerCount}张首页图片`,
          theme: 'warning'
        });
        return;
      }

      // 设置为首页图片，编号为当前数量+1
      img.bannerOrder = currentBannerCount + 1;
    }

    this.setData({ albumImages });
    this.updateSelectionCount();
  },

  // 退出批量删除模式并批量删除选中图片
  onCancelDeleteMode: function() {
    // 获取所有被选中的图片id和fileID
    const selected = this.data.albumImages.filter(img => img.selected);
    if (selected.length === 0) {
      // 没有选中任何图片，直接退出批量删除模式
      this.setData({ deleteMode: false });
      return;
    }
    // 弹窗确认
    wx.showModal({
      title: '批量删除',
      content: `确定要删除选中的${selected.length}张图片吗？`,
      confirmText: '删除',
      confirmColor: '#E34D59',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 1. 批量删除数据库记录
          const db = wx.cloud.database();
          const batchRemove = selected.map(img => db.collection('album_images').doc(img._id).remove());
          Promise.all(batchRemove).then(() => {
            // 2. 批量删除云存储文件
            const fileList = selected.map(img => img.fileID);
            wx.cloud.deleteFile({
              fileList,
              success: () => {
                showToast(this, { message: '删除成功', theme: 'success' });
                this.getAlbumImages(); // 刷新图片列表
                this.setData({ deleteMode: false });
              },
              fail: () => {
                showError(this, '云存储删除失败');
                this.setData({ deleteMode: false });
              }
            });
          }).catch(() => {
            showError(this, '数据库删除失败');
            this.setData({ deleteMode: false });
          });
        } else {
          // 用户取消，退出批量删除模式
          this.setData({ deleteMode: false });
        }
      }
    });
  },

  /**
   * 确认删除选中的图片 - Android风格
   *
   * 功能说明：
   * 点击底部操作栏的删除按钮时调用
   */
  onConfirmDelete: function() {
    // 获取所有选中的图片
    const selected = this.data.albumImages.filter(img => img.selected);
    if (selected.length === 0) {
      showToast(this, { message: '请先选择要删除的图片', theme: 'warning' });
      return;
    }

    // Android风格确认对话框
    wx.showModal({
      title: '删除照片',
      content: `确定要删除这 ${selected.length} 张照片吗？`,
      confirmText: '删除',
      confirmColor: '#e34d59',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.performBatchDelete(selected);
        }
      }
    });
  },

  /**
   * 执行批量删除操作
   */
  performBatchDelete: function(selected) {
    showLoading(this, '删除中...');

    // 1. 批量删除数据库记录
    const db = wx.cloud.database();
    const batchRemove = selected.map(img => db.collection('album_images').doc(img._id).remove());

    Promise.all(batchRemove).then(() => {
      // 2. 批量删除云存储文件
      const fileList = selected.map(img => img.fileID);
      wx.cloud.deleteFile({
        fileList,
        success: () => {
          hideToast(this);
          showToast(this, { message: '删除成功', theme: 'success' });
          this.loadAlbumImages(true); // 重新加载图片列表
          this.onCancelSelectionMode(); // 退出选择模式
        },
        fail: () => {
          hideToast(this);
          showError(this, '云存储删除失败');
        }
      });
    }).catch(() => {
      hideToast(this);
      showError(this, '数据库删除失败');
    });
  },

  /**
   * 分享选中的图片
   *
   * 功能说明：
   * Android相册风格的分享功能
   */
  onShareSelected: function() {
    const selected = this.data.albumImages.filter(img => img.selected);
    if (selected.length === 0) {
      showToast(this, { message: '请先选择要分享的图片', theme: 'warning' });
      return;
    }

    // 这里可以实现分享功能
    wx.showToast({
      title: `分享 ${selected.length} 张照片`,
      icon: 'none',
      duration: 1500
    });
  },

  /**
   * 移动选中的图片
   *
   * 功能说明：
   * 进入移动模式，准备将选中的图片移动到其他文件夹
   */
  onMoveSelected: function() {
    const selected = this.data.albumImages.filter(img => img.selected);
    if (selected.length === 0) {
      showToast(this, { message: '请先选择要移动的图片', theme: 'warning' });
      return;
    }

    this.setData({
      moveMode: true,
      deleteMode: false,
      showFolderSelector: true
    });
  },

  /**
   * 复制选中的图片
   *
   * 功能说明：
   * 进入复制模式，准备将选中的图片复制到其他文件夹
   */
  onCopySelected: function() {
    const selected = this.data.albumImages.filter(img => img.selected);
    if (selected.length === 0) {
      showToast(this, { message: '请先选择要复制的图片', theme: 'warning' });
      return;
    }

    this.setData({
      copyMode: true,
      deleteMode: false,
      showFolderSelector: true
    });
  },

  /**
   * 显示文件夹选择器
   */
  onShowFolderSelector: function() {
    this.setData({ showFolderSelector: true });
  },

  /**
   * 关闭文件夹选择器
   */
  onCloseFolderSelector: function() {
    this.setData({ showFolderSelector: false });
  },

  /**
   * 文件夹选择器可见性变化
   */
  onFolderSelectorChange: function(e) {
    this.setData({ showFolderSelector: e.detail.visible });
  },

  /**
   * 选择目标文件夹
   */
  onSelectTargetFolder: function(e) {
    const folder = e.currentTarget.dataset.folder;
    const selected = this.data.albumImages.filter(img => img.selected);

    if (this.data.moveMode) {
      this.movePhotosToFolder(selected, folder);
    } else if (this.data.copyMode) {
      this.copyPhotosToFolder(selected, folder);
    }
  },

  /**
   * 移动照片到指定文件夹
   */
  movePhotosToFolder: function(photos, targetFolder) {
    const db = wx.cloud.database();

    showLoading(this, '移动中...');

    const updatePromises = photos.map(photo =>
      db.collection('album_images').doc(photo._id).update({
        data: { folderId: targetFolder._id }
      })
    );

    Promise.all(updatePromises).then(() => {
      hideToast(this);
      showToast(this, {
        message: `成功移动 ${photos.length} 张照片到 ${targetFolder.name}`,
        theme: 'success'
      });
      this.loadAlbumImages(true); // 重新加载图片
      this.loadAlbumFolders(); // 重新加载文件夹
      this.onCancelSelectionMode(); // 退出选择模式
    }).catch(() => {
      hideToast(this);
      showError(this, '移动失败');
    });
  },

  /**
   * 复制照片到指定文件夹
   */
  copyPhotosToFolder: function(photos, targetFolder) {
    const db = wx.cloud.database();

    showLoading(this, '复制中...');

    const copyPromises = photos.map(photo =>
      db.collection('album_images').add({
        data: {
          fileID: photo.fileID,
          folderId: targetFolder._id,
          createTime: new Date(),
          // 复制时不保留首页图片设置
          bannerOrder: null
        }
      })
    );

    Promise.all(copyPromises).then(() => {
      hideToast(this);
      showToast(this, {
        message: `成功复制 ${photos.length} 张照片到 ${targetFolder.name}`,
        theme: 'success'
      });
      this.loadAlbumFolders(); // 重新加载文件夹
      this.onCancelSelectionMode(); // 退出选择模式
    }).catch(() => {
      hideToast(this);
      showError(this, '复制失败');
    });
  },

  /**
   * 在文件夹选择器中新建文件夹
   */
  onCreateNewFolder: function() {
    this.setData({ showFolderSelector: false });
    this.onCreateFolder();
  },

  /**
   * 确认首页图片选择
   */
  onConfirmBannerSelection: function() {
    const bannerImages = this.data.albumImages.filter(img => img.bannerOrder);
    if (bannerImages.length === 0) {
      showToast(this, { message: '请先选择首页图片', theme: 'warning' });
      return;
    }

    // 保存首页图片设置
    this.saveBannerSettings(bannerImages);
  },

  /**
   * 保存首页图片设置
   */
  saveBannerSettings: function(bannerImages) {
    showLoading(this, '保存中...');

    // 将选中的图片移动到首页文件夹
    const movePromises = bannerImages.map(img => this.moveImageToBanner(img));

    Promise.all(movePromises).then(() => {
      hideToast(this);
      showToast(this, { message: '首页图片设置成功', theme: 'success' });
      this.loadAlbumImages(true); // 重新加载图片列表
      this.onCancelSelectionMode(); // 退出选择模式
    }).catch(() => {
      hideToast(this);
      showError(this, '设置失败');
    });
  },

  /**
   * 将图片移动到首页文件夹
   */
  moveImageToBanner: async function(image) {
    try {
      // 1. 下载原图片
      const tempFileRes = await wx.cloud.downloadFile({
        fileID: image.fileID
      });

      // 2. 生成首页文件夹的新路径
      const timestamp = Date.now();
      const randomStr = Math.random().toString(36).substr(2, 9);
      const newCloudPath = `index-images/首页/${timestamp}_${randomStr}.jpg`;

      // 3. 上传到首页文件夹
      const uploadRes = await wx.cloud.uploadFile({
        cloudPath: newCloudPath,
        filePath: tempFileRes.tempFilePath
      });

      // 4. 更新数据库记录 - 保持向后兼容性
      const db = wx.cloud.database();
      await db.collection('album_images').doc(image._id).update({
        data: {
          fileID: uploadRes.fileID,
          bannerOrder: image.bannerOrder, // 保留bannerOrder字段，确保旧版本前端兼容
          category: 'banner', // 新增category字段，用于新版本的文件夹管理
          updateTime: new Date()
        }
      });

      // 5. 删除原文件
      await wx.cloud.deleteFile({
        fileList: [image.fileID]
      });

      return Promise.resolve();
    } catch (error) {
      console.error('移动图片到首页失败:', error);
      return Promise.reject(error);
    }
  },

  /**
   * 文件夹点击处理
   *
   * 功能说明：
   * 点击文件夹进入文件夹详情，显示文件夹内的照片
   */
  onFolderTap: function(e) {
    const folder = e.currentTarget.dataset.folder;
    console.log('点击文件夹:', folder);

    // 这里可以跳转到文件夹详情页面
    // 或者在当前页面显示文件夹内容
    wx.showToast({
      title: `打开${folder.name}`,
      icon: 'none',
      duration: 1500
    });
  },

  /**
   * 文件夹长按处理
   *
   * 功能说明：
   * 长按文件夹进入文件夹管理模式
   */
  onFolderLongPress: function(e) {
    const folder = e.currentTarget.dataset.folder;

    // 显示文件夹操作菜单
    wx.showActionSheet({
      itemList: ['重命名', '删除文件夹', '设为默认'],
      success: (res) => {
        switch(res.tapIndex) {
          case 0:
            this.onRenameFolder(folder);
            break;
          case 1:
            this.onDeleteFolder(folder);
            break;
          case 2:
            this.onSetDefaultFolder(folder);
            break;
        }
      }
    });
  },

  /**
   * 显示文件夹菜单
   *
   * 功能说明：
   * Android风格的右上角三点菜单
   * 包含新建文件夹等管理功能
   */
  onShowFolderMenu: function() {
    wx.showActionSheet({
      itemList: ['新建文件夹', '排序方式', '查看方式'],
      success: (res) => {
        switch(res.tapIndex) {
          case 0:
            this.onCreateFolder();
            break;
          case 1:
            this.onShowSortOptions();
            break;
          case 2:
            this.onShowViewOptions();
            break;
        }
      }
    });
  },

  /**
   * 显示照片菜单
   *
   * 功能说明：
   * 全部照片页面的菜单选项
   */
  onShowPhotoMenu: function() {
    wx.showActionSheet({
      itemList: ['选择首页图片', '批量收藏', '排序方式', '查看方式'],
      success: (res) => {
        switch(res.tapIndex) {
          case 0:
            this.onChooseBannerMode();
            break;
          case 1:
            this.onBatchFavoriteMode();
            break;
          case 2:
            this.onShowSortOptions();
            break;
          case 3:
            this.onShowViewOptions();
            break;
        }
      }
    });
  },

  /**
   * 显示排序选项
   */
  onShowSortOptions: function() {
    wx.showActionSheet({
      itemList: ['按时间排序', '按名称排序', '按大小排序'],
      success: (res) => {
        const sortTypes = ['time', 'name', 'size'];
        const sortType = sortTypes[res.tapIndex];
        wx.showToast({
          title: `按${['时间', '名称', '大小'][res.tapIndex]}排序`,
          icon: 'none'
        });
        // 这里可以实现实际的排序逻辑
      }
    });
  },

  /**
   * 显示查看选项
   */
  onShowViewOptions: function() {
    wx.showActionSheet({
      itemList: ['网格视图', '列表视图', '大图视图'],
      success: (res) => {
        const viewTypes = ['grid', 'list', 'large'];
        const viewType = viewTypes[res.tapIndex];
        wx.showToast({
          title: `切换到${['网格', '列表', '大图'][res.tapIndex]}视图`,
          icon: 'none'
        });
        // 这里可以实现实际的视图切换逻辑
      }
    });
  },

  /**
   * 批量收藏模式
   *
   * 功能说明：
   * 进入批量收藏选择模式
   */
  onBatchFavoriteMode: function() {
    this.setData({
      favoriteMode: true,
      deleteMode: false,
      chooseBannerMode: false,
      moveMode: false,
      copyMode: false
    });

    // 重置所有图片的选中状态
    const updatedImages = this.data.albumImages.map(img => ({
      ...img,
      selected: false
    }));

    this.setData({
      albumImages: updatedImages
    });

    showToast(this, { message: '请选择要收藏的图片', theme: 'info' });
  },

  /**
   * 执行批量收藏
   */
  onConfirmFavorite: function() {
    const selected = this.data.albumImages.filter(img => img.selected);

    if (selected.length === 0) {
      showToast(this, { message: '请先选择图片', theme: 'warning' });
      return;
    }

    wx.showModal({
      title: '批量收藏',
      content: `确定要收藏选中的${selected.length}张图片吗？`,
      confirmText: '收藏',
      confirmColor: '#0052d9',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.performBatchFavorite(selected);
        }
      }
    });
  },

  /**
   * 执行批量收藏操作
   */
  performBatchFavorite: function(selected) {
    showLoading(this, '收藏中...');

    const favoritePromises = selected.map(img => this.moveImageToFavorites(img));

    Promise.all(favoritePromises).then(() => {
      hideToast(this);
      showToast(this, { message: '收藏成功', theme: 'success' });
      this.loadAlbumImages(true); // 重新加载图片列表
      this.onCancelSelectionMode(); // 退出选择模式
    }).catch(() => {
      hideToast(this);
      showError(this, '收藏失败');
    });
  },

  /**
   * 将图片移动到收藏文件夹
   */
  moveImageToFavorites: async function(image) {
    try {
      // 1. 下载原图片
      const tempFileRes = await wx.cloud.downloadFile({
        fileID: image.fileID
      });

      // 2. 生成收藏文件夹的新路径
      const timestamp = Date.now();
      const randomStr = Math.random().toString(36).substr(2, 9);
      const newCloudPath = `index-images/收藏/${timestamp}_${randomStr}.jpg`;

      // 3. 上传到收藏文件夹
      const uploadRes = await wx.cloud.uploadFile({
        cloudPath: newCloudPath,
        filePath: tempFileRes.tempFilePath
      });

      // 4. 更新数据库记录 - 保持向后兼容性
      const db = wx.cloud.database();
      const updateData = {
        fileID: uploadRes.fileID,
        category: 'favorite',
        updateTime: new Date()
      };

      // 如果原图片有bannerOrder字段，保留它以确保向后兼容
      if (image.bannerOrder !== undefined && image.bannerOrder !== null) {
        updateData.bannerOrder = image.bannerOrder;
      }

      await db.collection('album_images').doc(image._id).update({
        data: updateData
      });

      // 5. 删除原文件
      await wx.cloud.deleteFile({
        fileList: [image.fileID]
      });

      return Promise.resolve();
    } catch (error) {
      console.error('移动图片到收藏失败:', error);
      return Promise.reject(error);
    }
  },

  /**
   * 将图片从收藏文件夹移回普通文件夹
   */
  moveImageFromFavorites: async function(image) {
    try {
      // 1. 下载原图片
      const tempFileRes = await wx.cloud.downloadFile({
        fileID: image.fileID
      });

      // 2. 生成普通文件夹的新路径
      const timestamp = Date.now();
      const randomStr = Math.random().toString(36).substr(2, 9);
      const newCloudPath = `index-images/${timestamp}_${randomStr}.jpg`;

      // 3. 上传到普通文件夹
      const uploadRes = await wx.cloud.uploadFile({
        cloudPath: newCloudPath,
        filePath: tempFileRes.tempFilePath
      });

      // 4. 更新数据库记录
      const updateData = {
        fileID: uploadRes.fileID,
        category: 'normal',
        updateTime: new Date()
      };

      // 如果原图片有bannerOrder字段，保留它
      if (image.bannerOrder !== undefined && image.bannerOrder !== null) {
        updateData.bannerOrder = image.bannerOrder;
      }

      await db.collection('album_images').doc(image._id).update({
        data: updateData
      });

      // 5. 删除原文件
      await wx.cloud.deleteFile({
        fileList: [image.fileID]
      });

      return Promise.resolve();
    } catch (error) {
      console.error('从收藏移除图片失败:', error);
      return Promise.reject(error);
    }
  },

  /**
   * 将图片移动到首页文件夹
   */
  moveImageToBanner: async function(image, bannerOrder) {
    try {
      // 1. 下载原图片
      const tempFileRes = await wx.cloud.downloadFile({
        fileID: image.fileID
      });

      // 2. 生成首页文件夹的新路径
      const timestamp = Date.now();
      const randomStr = Math.random().toString(36).substr(2, 9);
      const newCloudPath = `index-images/首页/${timestamp}_${randomStr}.jpg`;

      // 3. 上传到首页文件夹
      const uploadRes = await wx.cloud.uploadFile({
        cloudPath: newCloudPath,
        filePath: tempFileRes.tempFilePath
      });

      // 4. 更新数据库记录
      await db.collection('album_images').doc(image._id).update({
        data: {
          fileID: uploadRes.fileID,
          bannerOrder: bannerOrder,
          category: 'banner',
          updateTime: new Date()
        }
      });

      // 5. 删除原文件
      await wx.cloud.deleteFile({
        fileList: [image.fileID]
      });

      return Promise.resolve();
    } catch (error) {
      console.error('移动图片到首页失败:', error);
      return Promise.reject(error);
    }
  },

  /**
   * 将图片从首页文件夹移回普通文件夹
   */
  moveImageFromBanner: async function(image) {
    try {
      // 1. 下载原图片
      const tempFileRes = await wx.cloud.downloadFile({
        fileID: image.fileID
      });

      // 2. 生成普通文件夹的新路径
      const timestamp = Date.now();
      const randomStr = Math.random().toString(36).substr(2, 9);
      const newCloudPath = `index-images/${timestamp}_${randomStr}.jpg`;

      // 3. 上传到普通文件夹
      const uploadRes = await wx.cloud.uploadFile({
        cloudPath: newCloudPath,
        filePath: tempFileRes.tempFilePath
      });

      // 4. 更新数据库记录
      await db.collection('album_images').doc(image._id).update({
        data: {
          fileID: uploadRes.fileID,
          bannerOrder: null,
          category: 'normal',
          updateTime: new Date()
        }
      });

      // 5. 删除原文件
      await wx.cloud.deleteFile({
        fileList: [image.fileID]
      });

      return Promise.resolve();
    } catch (error) {
      console.error('从首页移除图片失败:', error);
      return Promise.reject(error);
    }
  },

  /**
   * 新建文件夹
   */
  onCreateFolder: function() {
    wx.showModal({
      title: '新建文件夹',
      content: '请输入文件夹名称',
      editable: true,
      placeholderText: '文件夹名称',
      success: (res) => {
        if (res.confirm && res.content.trim()) {
          this.createFolder(res.content.trim());
        }
      }
    });
  },

  /**
   * 创建文件夹
   */
  createFolder: function(name) {
    const db = wx.cloud.database();

    showLoading(this, '创建中...');

    db.collection('album_folders').add({
      data: {
        name: name,
        description: '',
        isDefault: false,
        createTime: new Date()
      }
    }).then(() => {
      hideToast(this);
      showToast(this, { message: '文件夹创建成功', theme: 'success' });
      this.loadAlbumFolders(); // 重新加载文件夹列表
    }).catch(() => {
      hideToast(this);
      showError(this, '创建失败');
    });
  },

  /**
   * 重命名文件夹
   */
  onRenameFolder: function(folder) {
    wx.showModal({
      title: '重命名文件夹',
      content: '请输入新的文件夹名称',
      editable: true,
      placeholderText: folder.name,
      success: (res) => {
        if (res.confirm && res.content.trim() && res.content.trim() !== folder.name) {
          this.renameFolder(folder._id, res.content.trim());
        }
      }
    });
  },

  /**
   * 执行重命名
   */
  renameFolder: function(folderId, newName) {
    const db = wx.cloud.database();

    showLoading(this, '重命名中...');

    db.collection('album_folders').doc(folderId).update({
      data: { name: newName }
    }).then(() => {
      hideToast(this);
      showToast(this, { message: '重命名成功', theme: 'success' });
      this.loadAlbumFolders();
    }).catch(() => {
      hideToast(this);
      showError(this, '重命名失败');
    });
  },

  /**
   * 删除文件夹
   */
  onDeleteFolder: function(folder) {
    if (folder.isDefault) {
      showToast(this, { message: '默认文件夹不能删除', theme: 'warning' });
      return;
    }

    wx.showModal({
      title: '删除文件夹',
      content: `确定要删除"${folder.name}"文件夹吗？文件夹内的照片将移动到默认文件夹。`,
      confirmText: '删除',
      confirmColor: '#e34d59',
      success: (res) => {
        if (res.confirm) {
          this.deleteFolder(folder);
        }
      }
    });
  },

  /**
   * 执行删除文件夹
   */
  deleteFolder: function(folder) {
    const db = wx.cloud.database();

    showLoading(this, '删除中...');

    // 1. 先将文件夹内的照片移动到默认文件夹
    // 2. 再删除文件夹
    this.movePhotosToDefault(folder._id).then(() => {
      return db.collection('album_folders').doc(folder._id).remove();
    }).then(() => {
      hideToast(this);
      showToast(this, { message: '文件夹删除成功', theme: 'success' });
      this.loadAlbumFolders();
    }).catch(() => {
      hideToast(this);
      showError(this, '删除失败');
    });
  },

  /**
   * 将照片移动到默认文件夹
   */
  movePhotosToDefault: function(folderId) {
    const db = wx.cloud.database();

    // 找到默认文件夹
    return db.collection('album_folders')
      .where({ isDefault: true })
      .get()
      .then(res => {
        if (res.data.length === 0) {
          throw new Error('找不到默认文件夹');
        }

        const defaultFolderId = res.data[0]._id;

        // 更新所有照片的文件夹ID
        return db.collection('album_images')
          .where({ folderId: folderId })
          .update({
            data: { folderId: defaultFolderId }
          });
      });
  },

  // 选择/取消首页图片（仅在选择模式下可用）
  onToggleBanner: function(e) {
    if (!this.data.chooseBannerMode) {
      // 不是选择模式，转为预览图片
      this.onPreviewImage(e);
      return;
    }
    const index = e.currentTarget.dataset.index;
    let { albumImages, maxBannerCount } = this.data;
    let img = albumImages[index];
    const selected = albumImages.filter(i => i.bannerOrder).sort((a, b) => a.bannerOrder - b.bannerOrder);
    if (img.bannerOrder) {
      const oldOrder = img.bannerOrder;
      img.bannerOrder = null;
      albumImages.forEach(i => {
        if (i.bannerOrder && i.bannerOrder > oldOrder) {
          i.bannerOrder--;
        }
      });
    } else {
      if (selected.length >= maxBannerCount) {
        showToast(this, { message: '最多只能选5张首页图片', theme: 'warning' });
        return;
      }
      img.bannerOrder = selected.length + 1;
    }
    showLoading(this, '保存中...');
    db.collection('album_images').doc(img._id).update({
      data: { bannerOrder: img.bannerOrder },
      success: () => {
        hideToast(this);
        this.getAlbumImages();
      },
      fail: () => {
        hideToast(this);
        showError(this, '保存失败');
      }
    });
  },

  // 上传图片
  onUploadImage: function() {
    // 防止重复上传
    if (this.data.isUploading) return;

    wx.chooseImage({
      count: 9, // 一次最多可选9张图片（小程序限制）
      sizeType: ['compressed'], // 使用压缩图片
      sourceType: ['album', 'camera'],
      success: chooseRes => {
        const filePaths = chooseRes.tempFilePaths; // 本地临时路径数组
        if (filePaths.length === 0) return;

        // 设置上传状态
        this.setData({ isUploading: true });

        // 用Promise.all批量上传所有图片
        const uploadTasks = filePaths.map(filePath => {
          // 生成云存储路径，默认放在根目录 index-images/
          const timestamp = Date.now();
          const randomStr = Math.random().toString(36).substr(2, 9);
          const cloudPath = `index-images/${timestamp}_${randomStr}.jpg`;

          return wx.cloud.uploadFile({
            cloudPath,
            filePath
          }).then(uploadRes => {
            // 上传成功后，将 fileID 存入数据库
            return db.collection('album_images').add({
              data: {
                fileID: uploadRes.fileID,
                createTime: new Date(),
                category: 'normal' // 标记为普通图片
              }
            });
          });
        });

        // 全部上传完成后刷新图片列表
        Promise.all(uploadTasks).then(() => {
          showToast(this, { message: '上传成功', theme: 'success' });
          this.loadAlbumImages(true); // 重新加载图片列表
        }).catch(error => {
          console.error('图片上传失败:', error);
          showError(this, '有图片上传失败');
        }).finally(() => {
          // 重置上传状态
          this.setData({ isUploading: false });
        });
      },
      fail: () => {
        // 用户取消选择图片
        this.setData({ isUploading: false });
      }
    });
  },

  // 删除图片（带确认弹窗）
  onDeleteImage: function(e) {
    const id = e.currentTarget.dataset.id; // 数据库记录id
    const fileID = e.currentTarget.dataset.fileid; // 云存储fileID
    // 弹窗提示用户确认
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张图片吗？',
      confirmText: '删除',
      confirmColor: '#E34D59', // 红色
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 用户点击了“删除”
          // 1. 先删除数据库记录
          db.collection('album_images').doc(id).remove({
            success: () => {
              // 2. 再删除云存储文件
              wx.cloud.deleteFile({
                fileList: [fileID],
                success: () => {
                  showToast(this, { message: '删除成功', theme: 'success' });
                  this.getAlbumImages(); // 刷新图片列表
                },
                fail: () => {
                  showError(this, '云存储删除失败');
                }
              });
            },
            fail: () => {
              showError(this, '数据库删除失败');
            }
          });
        }
        // 用户点击“取消”则不做任何操作
      }
    });
  },

  // 预览图片（仅在非选择/删除模式下可用）
  onPreviewImage: function(e) {
    if (this.data.chooseBannerMode || this.data.deleteMode || this.data.favoriteMode) return; // 选择模式下不响应
    const index = e.currentTarget.dataset.index;

    // 打开Android风格的图片预览弹窗
    this.setData({
      showImagePreview: true,
      previewCurrentIndex: index,
      previewCurrentImage: this.data.albumImages[index] || {},
      showPreviewControls: true
    });
  },

  // ==================== Android风格图片预览相关函数 ====================

  /**
   * 关闭图片预览弹窗
   */
  onClosePreview: function() {
    this.setData({
      showImagePreview: false,
      previewCurrentIndex: 0,
      previewCurrentImage: {},
      showPreviewControls: true
    });
  },

  /**
   * 预览弹窗可见性变化
   */
  onPreviewVisibleChange: function(e) {
    if (!e.detail.visible) {
      this.onClosePreview();
    }
  },

  /**
   * 预览轮播图切换
   */
  onPreviewSwiperChange: function(e) {
    const currentIndex = e.detail.current;
    this.setData({
      previewCurrentIndex: currentIndex,
      previewCurrentImage: this.data.albumImages[currentIndex] || {}
    });
  },

  /**
   * 切换预览控制栏显示/隐藏
   */
  onTogglePreviewControls: function() {
    this.setData({
      showPreviewControls: !this.data.showPreviewControls
    });
  },

  /**
   * 预览模式下收藏/取消收藏
   */
  onPreviewToggleFavorite: function() {
    const currentImage = this.data.previewCurrentImage;
    const isFavorite = currentImage.category === 'favorite';

    if (isFavorite) {
      this.previewRemoveFromFavorites(currentImage);
    } else {
      this.previewAddToFavorites(currentImage);
    }
  },

  /**
   * 预览模式下添加到收藏
   */
  previewAddToFavorites: async function(image) {
    this.setData({ previewLoading: true });

    try {
      await this.moveImageToFavorites(image);

      // 更新当前图片列表和预览数据
      const updatedImages = this.data.albumImages.map(img =>
        img._id === image._id ? { ...img, category: 'favorite' } : img
      );

      this.setData({
        albumImages: updatedImages,
        previewCurrentImage: { ...image, category: 'favorite' },
        previewLoading: false
      });

      showToast(this, { message: '已添加到收藏', theme: 'success' });
    } catch (error) {
      this.setData({ previewLoading: false });
      showToast(this, { message: '收藏失败', theme: 'error' });
    }
  },

  /**
   * 预览模式下从收藏中移除
   */
  previewRemoveFromFavorites: async function(image) {
    this.setData({ previewLoading: true });

    try {
      await this.moveImageFromFavorites(image);

      // 更新当前图片列表和预览数据
      const updatedImages = this.data.albumImages.map(img =>
        img._id === image._id ? { ...img, category: 'normal' } : img
      );

      this.setData({
        albumImages: updatedImages,
        previewCurrentImage: { ...image, category: 'normal' },
        previewLoading: false
      });

      showToast(this, { message: '已从收藏中移除', theme: 'success' });
    } catch (error) {
      this.setData({ previewLoading: false });
      showToast(this, { message: '操作失败', theme: 'error' });
    }
  },

  /**
   * 预览模式下设为首页/取消首页
   */
  onPreviewToggleBanner: function() {
    const currentImage = this.data.previewCurrentImage;
    const isBanner = currentImage.bannerOrder;

    if (isBanner) {
      this.previewRemoveFromBanner(currentImage);
    } else {
      this.previewAddToBanner(currentImage);
    }
  },

  /**
   * 预览模式下设为首页图片
   */
  previewAddToBanner: async function(image) {
    this.setData({ previewLoading: true });

    try {
      // 检查首页图片数量限制
      const bannerCount = this.data.albumImages.filter(img => img.bannerOrder).length;

      if (bannerCount >= this.data.maxBannerCount) {
        this.setData({ previewLoading: false });
        showToast(this, {
          message: `最多只能设置${this.data.maxBannerCount}张首页图片`,
          theme: 'warning'
        });
        return;
      }

      await this.moveImageToBanner(image, bannerCount + 1);

      // 更新当前图片列表和预览数据
      const updatedImages = this.data.albumImages.map(img =>
        img._id === image._id ? { ...img, category: 'banner', bannerOrder: bannerCount + 1 } : img
      );

      this.setData({
        albumImages: updatedImages,
        previewCurrentImage: { ...image, category: 'banner', bannerOrder: bannerCount + 1 },
        previewLoading: false
      });

      showToast(this, { message: '已设为首页图片', theme: 'success' });
    } catch (error) {
      this.setData({ previewLoading: false });
      showToast(this, { message: '设置失败', theme: 'error' });
    }
  },

  /**
   * 预览模式下从首页图片中移除
   */
  previewRemoveFromBanner: async function(image) {
    this.setData({ previewLoading: true });

    try {
      await this.moveImageFromBanner(image);

      // 更新当前图片列表和预览数据
      const updatedImages = this.data.albumImages.map(img =>
        img._id === image._id ? { ...img, category: 'normal', bannerOrder: null } : img
      );

      this.setData({
        albumImages: updatedImages,
        previewCurrentImage: { ...image, category: 'normal', bannerOrder: null },
        previewLoading: false
      });

      showToast(this, { message: '已从首页图片中移除', theme: 'success' });
    } catch (error) {
      this.setData({ previewLoading: false });
      showToast(this, { message: '操作失败', theme: 'error' });
    }
  },

  /**
   * 预览模式下分享图片
   */
  onPreviewShare: function() {
    const currentImage = this.data.previewCurrentImage;

    wx.showActionSheet({
      itemList: ['保存到相册', '发送给朋友'],
      success: (res) => {
        switch(res.tapIndex) {
          case 0:
            this.previewSaveToAlbum(currentImage);
            break;
          case 1:
            this.previewShareToFriend(currentImage);
            break;
        }
      }
    });
  },

  /**
   * 预览模式下保存到相册
   */
  previewSaveToAlbum: function(image) {
    wx.downloadFile({
      url: image.tempFileURL,
      success: (res) => {
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            showToast(this, { message: '已保存到相册', theme: 'success' });
          },
          fail: () => {
            showToast(this, { message: '保存失败', theme: 'error' });
          }
        });
      },
      fail: () => {
        showToast(this, { message: '下载失败', theme: 'error' });
      }
    });
  },

  /**
   * 预览模式下发送给朋友
   */
  previewShareToFriend: function(image) {
    // 这里可以实现分享逻辑
    showToast(this, { message: '分享功能开发中', theme: 'info' });
  },

  /**
   * 预览模式下删除图片
   */
  onPreviewDelete: function() {
    const currentImage = this.data.previewCurrentImage;

    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这张图片吗？',
      confirmText: '删除',
      confirmColor: '#e34d59',
      success: (res) => {
        if (res.confirm) {
          this.previewDeleteImage(currentImage);
        }
      }
    });
  },

  /**
   * 预览模式下执行删除操作
   */
  previewDeleteImage: async function(image) {
    this.setData({ previewLoading: true });

    try {
      // 删除云存储文件
      await wx.cloud.deleteFile({
        fileList: [image.fileID]
      });

      // 删除数据库记录
      await db.collection('album_images').doc(image._id).remove();

      // 从当前列表中移除
      const updatedImages = this.data.albumImages.filter(img => img._id !== image._id);

      if (updatedImages.length === 0) {
        // 没有图片了，关闭预览
        showToast(this, { message: '删除成功', theme: 'success' });
        this.onClosePreview();
        this.setData({ albumImages: updatedImages });
        return;
      }

      // 调整当前索引
      let newIndex = this.data.previewCurrentIndex;
      if (newIndex >= updatedImages.length) {
        newIndex = updatedImages.length - 1;
      }

      this.setData({
        albumImages: updatedImages,
        previewCurrentIndex: newIndex,
        previewCurrentImage: updatedImages[newIndex],
        previewLoading: false
      });

      showToast(this, { message: '删除成功', theme: 'success' });
    } catch (error) {
      this.setData({ previewLoading: false });
      showToast(this, { message: '删除失败', theme: 'error' });
    }
  },

  /**
   * 预览模式下更多操作
   */
  onPreviewMoreActions: function() {
    wx.showActionSheet({
      itemList: ['查看详情', '重命名', '复制'],
      success: (res) => {
        switch(res.tapIndex) {
          case 0:
            this.showPreviewImageDetails();
            break;
          case 1:
            showToast(this, { message: '重命名功能开发中', theme: 'info' });
            break;
          case 2:
            showToast(this, { message: '复制功能开发中', theme: 'info' });
            break;
        }
      }
    });
  },

  /**
   * 显示预览图片详情
   */
  showPreviewImageDetails: function() {
    const image = this.data.previewCurrentImage;
    const createTime = new Date(image.createTime).toLocaleString();

    wx.showModal({
      title: '图片详情',
      content: `创建时间：${createTime}\n类型：${this.getCategoryText(image.category)}\n${image.bannerOrder ? `首页顺序：${image.bannerOrder}` : ''}`,
      showCancel: false
    });
  },

  /**
   * 显示预览更多菜单
   */
  onShowPreviewMoreMenu: function() {
    wx.showActionSheet({
      itemList: ['查看详情', '设置壁纸'],
      success: (res) => {
        switch(res.tapIndex) {
          case 0:
            this.showPreviewImageDetails();
            break;
          case 1:
            showToast(this, { message: '设置壁纸功能开发中', theme: 'info' });
            break;
        }
      }
    });
  },

  /**
   * 预览图片加载完成
   */
  onPreviewImageLoad: function() {
    // 图片加载完成的处理
  },

  /**
   * 预览图片加载失败
   */
  onPreviewImageError: function() {
    showToast(this, { message: '图片加载失败', theme: 'error' });
  },

  /**
   * 获取分类文本
   */
  getCategoryText: function(category) {
    switch(category) {
      case 'favorite': return '收藏图片';
      case 'banner': return '首页图片';
      case 'normal':
      default: return '普通图片';
    }
  }

});