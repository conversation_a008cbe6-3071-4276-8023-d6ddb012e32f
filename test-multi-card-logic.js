// test-multi-card-logic.js
// 多卡支持和智能扣减策略测试用例

// 模拟数据库操作
const mockDB = {
  collection: (name) => ({
    where: (condition) => ({
      get: async () => {
        if (name === 'membershipCard') {
          return { data: mockMembershipCards.filter(card => {
            // 模拟数据库查询条件
            if (condition.userId && card.userId !== condition.userId) return false;
            if (condition.status && card.status !== condition.status) return false;
            if (condition.remainingTimes && condition.remainingTimes.gt !== undefined && card.remainingTimes <= condition.remainingTimes.gt) return false;

            // 模拟时间条件 - 修复时间比较逻辑
            const now = new Date();
            if (condition.validFrom && condition.validFrom.lte !== undefined) {
              if (new Date(card.validFrom) > now) return false;
            }
            if (condition.validTo && condition.validTo.gte !== undefined) {
              if (new Date(card.validTo) < now) return false;
            }

            return true;
          })};
        }
        return { data: [] };
      }
    }),
    doc: (id) => ({
      update: async (data) => {
        const card = mockMembershipCards.find(c => c._id === id);
        if (card) {
          Object.assign(card, data.data);
        }
        return { success: true };
      }
    })
  })
};

// 模拟会员卡数据 - 多卡场景
let mockMembershipCards = [
  {
    _id: 'card1',
    cardNumber: '202412010001',
    userId: 'user123',
    totalTimes: 10,
    remainingTimes: 3,
    validFrom: new Date('2024-12-01'),
    validTo: new Date('2025-01-31'), // 即将到期
    status: '正常',
    createTime: new Date('2024-12-01'),
    updateTime: new Date('2024-12-01')
  },
  {
    _id: 'card2',
    cardNumber: '202412150001',
    userId: 'user123',
    totalTimes: 20,
    remainingTimes: 15,
    validFrom: new Date('2024-12-15'),
    validTo: new Date('2025-03-31'), // 较晚到期
    status: '正常',
    createTime: new Date('2024-12-15'),
    updateTime: new Date('2024-12-15')
  },
  {
    _id: 'card3',
    cardNumber: '202411010001',
    userId: 'user123',
    totalTimes: 5,
    remainingTimes: 0, // 已用完
    validFrom: new Date('2024-11-01'),
    validTo: new Date('2025-02-28'),
    status: '正常',
    createTime: new Date('2024-11-01'),
    updateTime: new Date('2024-12-20')
  },
  {
    _id: 'card4',
    cardNumber: '202410010001',
    userId: 'user123',
    totalTimes: 8,
    remainingTimes: 2,
    validFrom: new Date('2024-10-01'),
    validTo: new Date('2025-12-31'), // 修改为未来日期，确保有效
    status: '正常',
    createTime: new Date('2024-10-01'),
    updateTime: new Date('2024-12-01')
  }
];

// 导入工具函数（在实际环境中）
// import { getValidMembershipCards, deductMembershipCard, calculateCardStatus } from './miniprogram/utils/membershipCardUtils.js';

// 模拟工具函数
async function getValidMembershipCards(userId, db) {
  const now = new Date();
  const result = await db.collection('membershipCard')
    .where({
      userId: userId,
      status: '正常',
      validFrom: { lte: now },
      validTo: { gte: now },
      remainingTimes: { gt: 0 }
    })
    .get();
  
  // 按智能扣减策略排序
  const sortedCards = result.data.sort((a, b) => {
    // 首先按到期时间升序排序（越早到期的越优先）
    const timeA = new Date(a.validTo).getTime();
    const timeB = new Date(b.validTo).getTime();
    
    if (timeA !== timeB) {
      return timeA - timeB;
    }
    
    // 如果到期时间相同，按创建时间升序排序（先创建的先扣减）
    const createTimeA = new Date(a.createTime).getTime();
    const createTimeB = new Date(b.createTime).getTime();
    
    return createTimeA - createTimeB;
  });
  
  return sortedCards;
}

async function deductMembershipCard(userId, db) {
  try {
    const validCards = await getValidMembershipCards(userId, db);
    
    if (validCards.length === 0) {
      return {
        success: false,
        message: '没有有效的会员卡，无法扣减次数'
      };
    }
    
    const selectedCard = validCards[0];
    
    if (selectedCard.remainingTimes <= 0) {
      return {
        success: false,
        message: '会员卡剩余次数不足'
      };
    }
    
    // 扣减次数
    await db.collection('membershipCard').doc(selectedCard._id).update({
      data: {
        remainingTimes: selectedCard.remainingTimes - 1,
        updateTime: new Date()
      }
    });
    
    return {
      success: true,
      card: {
        ...selectedCard,
        remainingTimes: selectedCard.remainingTimes - 1
      },
      message: `已扣减会员卡 ${selectedCard.cardNumber} 1次，剩余 ${selectedCard.remainingTimes - 1} 次`
    };
    
  } catch (error) {
    console.error('扣减会员卡次数失败:', error);
    return {
      success: false,
      message: '扣减失败: ' + error.message
    };
  }
}

// 测试用例

// 测试1: 验证智能扣减策略
async function testSmartDeduction() {
  console.log('\n=== 测试智能扣减策略 ===');
  
  // 重置数据
  mockMembershipCards[0].remainingTimes = 3; // card1: 2025-01-31到期
  mockMembershipCards[1].remainingTimes = 15; // card2: 2025-03-31到期
  
  console.log('初始状态:');
  console.log('Card1 (2025-01-31到期):', mockMembershipCards[0].remainingTimes, '次');
  console.log('Card2 (2025-03-31到期):', mockMembershipCards[1].remainingTimes, '次');
  
  // 第一次扣减 - 应该扣减card1（更早到期）
  const result1 = await deductMembershipCard('user123', mockDB);
  console.log('\n第一次扣减结果:', result1.success ? '成功' : '失败');
  console.log('扣减的卡片:', result1.card?.cardNumber);
  console.log('Card1剩余:', mockMembershipCards[0].remainingTimes, '次');
  console.log('Card2剩余:', mockMembershipCards[1].remainingTimes, '次');
  
  // 继续扣减直到card1用完
  for (let i = 0; i < 3; i++) {
    const result = await deductMembershipCard('user123', mockDB);
    console.log(`\n第${i+2}次扣减 - 卡片: ${result.card?.cardNumber}, Card1剩余: ${mockMembershipCards[0].remainingTimes}, Card2剩余: ${mockMembershipCards[1].remainingTimes}`);
  }
  
  // 下一次扣减应该使用card2
  const result2 = await deductMembershipCard('user123', mockDB);
  console.log('\nCard1用完后扣减 - 卡片:', result2.card?.cardNumber);
  console.log('Card2剩余:', mockMembershipCards[1].remainingTimes, '次');
}

// 测试2: 验证多卡查询和排序
async function testMultiCardQuery() {
  console.log('\n=== 测试多卡查询和排序 ===');
  
  const validCards = await getValidMembershipCards('user123', mockDB);
  
  console.log('有效会员卡数量:', validCards.length);
  console.log('排序结果:');
  validCards.forEach((card, index) => {
    console.log(`${index + 1}. 卡号: ${card.cardNumber}, 到期: ${card.validTo.toISOString().split('T')[0]}, 剩余: ${card.remainingTimes}次`);
  });
  
  // 验证排序是否正确
  if (validCards.length >= 2) {
    const card1ExpireTime = new Date(validCards[0].validTo).getTime();
    const card2ExpireTime = new Date(validCards[1].validTo).getTime();
    
    if (card1ExpireTime <= card2ExpireTime) {
      console.log('✅ 排序正确：优先扣减到期时间更早的卡片');
    } else {
      console.log('❌ 排序错误：应该优先扣减到期时间更早的卡片');
    }
  }
}

// 测试3: 验证边界情况
async function testEdgeCases() {
  console.log('\n=== 测试边界情况 ===');
  
  // 情况1: 所有卡片都用完
  mockMembershipCards.forEach(card => {
    if (card.userId === 'user123') {
      card.remainingTimes = 0;
    }
  });
  
  const result1 = await deductMembershipCard('user123', mockDB);
  console.log('所有卡片用完时扣减:', result1.success ? '成功' : '失败');
  console.log('错误信息:', result1.message);
  
  // 情况2: 恢复一张卡片，测试单卡扣减
  mockMembershipCards[1].remainingTimes = 1;
  
  const result2 = await deductMembershipCard('user123', mockDB);
  console.log('\n只有一张有效卡时扣减:', result2.success ? '成功' : '失败');
  console.log('扣减的卡片:', result2.card?.cardNumber);
  
  // 情况3: 测试并发安全（模拟）
  console.log('\n模拟并发扣减测试...');
  mockMembershipCards[1].remainingTimes = 2;
  
  const promises = [
    deductMembershipCard('user123', mockDB),
    deductMembershipCard('user123', mockDB),
    deductMembershipCard('user123', mockDB) // 这个应该失败
  ];
  
  const results = await Promise.all(promises);
  console.log('并发扣减结果:');
  results.forEach((result, index) => {
    console.log(`请求${index + 1}: ${result.success ? '成功' : '失败'} - ${result.message}`);
  });
}

// 测试4: 验证相同到期时间的排序
async function testSameExpirationSort() {
  console.log('\n=== 测试相同到期时间的排序 ===');
  
  // 创建两张到期时间相同但创建时间不同的卡片
  mockMembershipCards = [
    {
      _id: 'card_same1',
      cardNumber: '202412010001',
      userId: 'user123',
      totalTimes: 10,
      remainingTimes: 5,
      validFrom: new Date('2024-12-01'),
      validTo: new Date('2025-02-28'), // 相同到期时间
      status: '正常',
      createTime: new Date('2024-12-01'), // 较早创建
      updateTime: new Date('2024-12-01')
    },
    {
      _id: 'card_same2',
      cardNumber: '202412150001',
      userId: 'user123',
      totalTimes: 10,
      remainingTimes: 8,
      validFrom: new Date('2024-12-15'),
      validTo: new Date('2025-02-28'), // 相同到期时间
      status: '正常',
      createTime: new Date('2024-12-15'), // 较晚创建
      updateTime: new Date('2024-12-15')
    }
  ];
  
  const validCards = await getValidMembershipCards('user123', mockDB);
  console.log('相同到期时间的卡片排序:');
  validCards.forEach((card, index) => {
    console.log(`${index + 1}. 卡号: ${card.cardNumber}, 创建时间: ${card.createTime.toISOString().split('T')[0]}, 剩余: ${card.remainingTimes}次`);
  });
  
  // 验证应该优先扣减较早创建的卡片
  const result = await deductMembershipCard('user123', mockDB);
  console.log('\n扣减结果 - 卡片:', result.card?.cardNumber);
  
  if (result.card?.cardNumber === '202412010001') {
    console.log('✅ 排序正确：优先扣减较早创建的卡片');
  } else {
    console.log('❌ 排序错误：应该优先扣减较早创建的卡片');
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('🧪 开始多卡支持和智能扣减策略测试\n');
  
  try {
    await testMultiCardQuery();
    await testSmartDeduction();
    await testSameExpirationSort();
    await testEdgeCases();
    
    console.log('\n✅ 所有测试完成');
  } catch (error) {
    console.error('\n❌ 测试过程中出现错误:', error);
  }
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runAllTests,
    testSmartDeduction,
    testMultiCardQuery,
    testEdgeCases,
    testSameExpirationSort
  };
}

// 如果直接运行此文件
if (typeof window === 'undefined' && require.main === module) {
  runAllTests();
}
