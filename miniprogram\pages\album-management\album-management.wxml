<t-toast id="t-toast" />
<!-- 相册管理页面 -->
<view class="album-management-container">


  <!-- 选择模式顶部栏 - Android风格 -->
  <view class="selection-header" wx:if="{{(currentCategory === 'all' || currentCategory === 'folders') && (deleteMode || chooseBannerMode || moveMode || copyMode || favoriteMode)}}">
    <view class="selection-info">
      <t-icon name="close" size="20" color="#666" bindtap="onCancelSelectionMode" />
      <text class="selection-count" wx:if="{{deleteMode}}">已选择 {{selectedCount}} 项</text>
      <text class="selection-count" wx:if="{{chooseBannerMode}}">选择首页图片 ({{bannerSelectedCount}}/{{maxBannerCount}})</text>
      <text class="selection-count" wx:if="{{moveMode}}">移动 {{selectedCount}} 项</text>
      <text class="selection-count" wx:if="{{copyMode}}">复制 {{selectedCount}} 项</text>
      <text class="selection-count" wx:if="{{favoriteMode}}">收藏 {{selectedCount}} 项</text>
    </view>
  </view>

  <!-- 文件夹页面右上角菜单按钮 -->
  <view class="page-menu" wx:if="{{currentCategory === 'folders' && !deleteMode && !moveMode && !copyMode}}">
    <t-icon name="ellipsis" size="24" color="#666" bindtap="onShowFolderMenu" />
  </view>

  <!-- 全部照片页面右上角菜单按钮 -->
  <view class="page-menu" wx:if="{{currentCategory === 'all' && !deleteMode && !chooseBannerMode}}">
    <t-icon name="ellipsis" size="24" color="#666" bindtap="onShowPhotoMenu" />
  </view>

  <!-- 相册内容区域 -->
  <view class="album-content">


    <!-- 收藏相册视图 -->
    <view wx:if="{{currentCategory === 'favorites'}}" class="favorite-albums">
      <view class="album-card" wx:for="{{favoriteAlbums}}" wx:key="id" data-album="{{item}}" bindtap="onAlbumTap">
        <view class="album-cover">
          <image src="{{item.coverImage}}" mode="aspectFill" class="cover-image" />
          <view class="album-info">
            <text class="album-name">{{item.name}}</text>
            <text class="album-count">{{item.count}}张照片</text>
          </view>
          <view class="favorite-badge">
            <t-icon name="heart-filled" size="16" color="#ff4757" />
          </view>
        </view>
      </view>
    </view>

    <!-- 文件夹视图 - Android风格 -->
    <view wx:if="{{currentCategory === 'folders'}}" class="folder-view">
      <!-- 文件夹列表 -->
      <view class="folder-list">
        <view class="folder-card" wx:for="{{albumFolders}}" wx:key="_id" data-folder="{{item}}" bindtap="onFolderTap" bindlongpress="onFolderLongPress">
          <view class="folder-cover">
            <image wx:if="{{item.coverImage}}" src="{{item.coverImage}}" mode="aspectFill" class="cover-image" />
            <view wx:else class="empty-folder">
              <t-icon name="folder" size="48" color="#ccc" />
            </view>
            <view class="folder-info">
              <text class="folder-name">{{item.name}}</text>
              <text class="folder-count">{{item.count}}张照片</text>
            </view>
            <!-- 默认文件夹标识 -->
            <view wx:if="{{item.isDefault}}" class="default-badge">
              <t-icon name="star-filled" size="16" color="#ffa500" />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 全部相册视图 - 保持原有的图片网格布局 -->
    <view wx:if="{{currentCategory === 'all'}}" class="all-albums">
      <view class="album-grid">
        <!-- 上传图片按钮 - 放在第一个位置 -->
        <view class="album-item upload-item" bindtap="onUploadImage">
          <view class="upload-btn-container">
            <t-icon name="add" size="32" color="#999" />
            <text class="upload-text">上传图片</text>
          </view>
          <!-- 上传状态提示 -->
          <view class="upload-status" wx:if="{{isUploading}}">
            <t-loading size="16" />
            <text class="uploading-text">上传中...</text>
          </view>
        </view>

        <!-- 已上传的图片 -->
        <view class="album-item" wx:for="{{albumImages}}" wx:key="_id">
          <!-- 首页编号圆圈 -->
          <view wx:if="{{item.bannerOrder && !deleteMode}}" class="banner-order-badge">{{item.bannerOrder}}</view>

          <!-- 选择模式下的圆形选择框 - Android风格 -->
          <view wx:if="{{deleteMode || chooseBannerMode || favoriteMode}}" class="selection-circle {{item.selected ? 'selected' : ''}}" data-index="{{index}}" bindtap="onSelectImage">
            <t-icon wx:if="{{item.selected}}" name="check" size="16" color="#fff" />
          </view>

          <!-- 图片 -->
          <image
            src="{{item.tempFileURL}}"
            mode="aspectFill"
            class="album-img"
            data-index="{{index}}"
            bindtap="{{deleteMode || chooseBannerMode || favoriteMode ? 'onSelectImage' : 'onPreviewImage'}}"
            bindlongpress="onImageLongPress"
          />
        </view>

        <!-- 加载状态 -->
        <view wx:if="{{loading}}" class="loading-item">
          <t-loading size="24" />
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 没有更多提示 - 占用一张图片的位置 -->
        <view wx:if="{{!hasMore && albumImages.length > 0}}" class="album-item end-item">
          <view class="end-content">
            <t-icon name="check-circle" size="24" color="#999" />
            <text class="end-text">没有更多了</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 提示信息 -->
  <view class="album-warning" wx:if="{{currentCategory === 'all' && !deleteMode && !chooseBannerMode}}">
    <text><t-icon name="info-circle" size="16" color="#fa7d3c" /> 相册中的</text><text class="highlight">全部</text><text>图片对</text><text class="highlight">任何</text><text>用户都</text><text class="highlight">可见</text><text>，用户可在首页点击静态图片，翻阅整个相册</text>
  </view>

  <!-- 底部操作栏 - Android风格 -->
  <view class="bottom-action-bar" wx:if="{{(currentCategory === 'all' || currentCategory === 'folders') && (deleteMode || chooseBannerMode || moveMode || copyMode || favoriteMode)}}">
    <view class="action-bar-content">
      <!-- 删除模式操作 -->
      <view wx:if="{{deleteMode}}" class="action-buttons">
        <view class="action-button" bindtap="onConfirmDelete">
          <t-icon name="delete" size="24" color="#e34d59" />
          <text class="action-text">删除</text>
        </view>
        <view class="action-button" bindtap="onShareSelected">
          <t-icon name="share" size="24" color="#666" />
          <text class="action-text">分享</text>
        </view>
        <view class="action-button" bindtap="onMoveSelected">
          <t-icon name="folder" size="24" color="#666" />
          <text class="action-text">移动</text>
        </view>
        <view class="action-button" bindtap="onCopySelected">
          <t-icon name="file-copy" size="24" color="#666" />
          <text class="action-text">复制</text>
        </view>
      </view>

      <!-- 首页图片选择模式操作 -->
      <view wx:if="{{chooseBannerMode}}" class="action-buttons">
        <view class="action-button" bindtap="onConfirmBannerSelection">
          <t-icon name="check" size="24" color="#0052d9" />
          <text class="action-text">确认选择</text>
        </view>
      </view>

      <!-- 移动模式操作 -->
      <view wx:if="{{moveMode}}" class="action-buttons">
        <view class="action-button" bindtap="onShowFolderSelector">
          <t-icon name="folder" size="24" color="#0052d9" />
          <text class="action-text">选择文件夹</text>
        </view>
      </view>

      <!-- 复制模式操作 -->
      <view wx:if="{{copyMode}}" class="action-buttons">
        <view class="action-button" bindtap="onShowFolderSelector">
          <t-icon name="folder" size="24" color="#0052d9" />
          <text class="action-text">选择文件夹</text>
        </view>
      </view>

      <!-- 收藏模式操作 -->
      <view wx:if="{{favoriteMode}}" class="action-buttons">
        <view class="action-button" bindtap="onConfirmFavorite">
          <t-icon name="heart" size="24" color="#ff4757" />
          <text class="action-text">收藏</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 文件夹选择器弹窗 -->
  <t-popup visible="{{showFolderSelector}}" placement="bottom" bind:visible-change="onFolderSelectorChange">
    <view class="folder-selector">
      <view class="selector-header">
        <text class="selector-title">{{moveMode ? '移动到文件夹' : '复制到文件夹'}}</text>
        <t-icon name="close" size="20" color="#666" bindtap="onCloseFolderSelector" />
      </view>
      <view class="selector-content">
        <view class="folder-option" wx:for="{{albumFolders}}" wx:key="_id" data-folder="{{item}}" bindtap="onSelectTargetFolder">
          <t-icon name="folder" size="20" color="#666" />
          <text class="folder-option-name">{{item.name}}</text>
          <text class="folder-option-count">({{item.count}})</text>
        </view>
        <view class="folder-option" bindtap="onCreateNewFolder">
          <t-icon name="add" size="20" color="#0052d9" />
          <text class="folder-option-name new-folder">新建文件夹</text>
        </view>
      </view>
    </view>
  </t-popup>

  <!-- 底部导航栏 - Android风格 -->
  <view class="bottom-navigation">
    <view class="nav-tabs">
      <view
        class="nav-tab {{currentCategory === 'favorites' ? 'active' : ''}}"
        data-category="favorites"
        bindtap="onCategoryChange">
        <t-icon name="heart" size="20" color="{{currentCategory === 'favorites' ? '#0052d9' : '#999'}}" />
        <text class="nav-text">收藏</text>
      </view>
      <view
        class="nav-tab {{currentCategory === 'folders' ? 'active' : ''}}"
        data-category="folders"
        bindtap="onCategoryChange">
        <t-icon name="folder" size="20" color="{{currentCategory === 'folders' ? '#0052d9' : '#999'}}" />
        <text class="nav-text">文件夹</text>
      </view>
      <view
        class="nav-tab {{currentCategory === 'all' ? 'active' : ''}}"
        data-category="all"
        bindtap="onCategoryChange">
        <t-icon name="view-module" size="20" color="{{currentCategory === 'all' ? '#0052d9' : '#999'}}" />
        <text class="nav-text">全部照片</text>
      </view>
    </view>
  </view>

  <!-- Android风格图片预览弹窗 -->
  <t-popup
    visible="{{showImagePreview}}"
    placement="center"
    bind:visible-change="onPreviewVisibleChange"
    show-overlay="{{true}}"
    overlay-props="{{overlayProps}}"
    z-index="{{9999}}"
  >
    <view class="image-preview-popup">
      <!-- 顶部状态栏 -->
      <view class="preview-header {{showPreviewControls ? 'visible' : 'hidden'}}">
        <view class="header-left">
          <t-icon name="close" size="24" color="#fff" bindtap="onClosePreview" />
        </view>
        <view class="header-center">
          <text class="image-counter">{{previewCurrentIndex + 1}} / {{albumImages.length}}</text>
        </view>
        <view class="header-right">
          <t-icon name="more" size="24" color="#fff" bindtap="onShowPreviewMoreMenu" />
        </view>
      </view>

      <!-- 图片轮播区域 -->
      <swiper
        class="preview-swiper"
        current="{{previewCurrentIndex}}"
        bindchange="onPreviewSwiperChange"
        bindtap="onTogglePreviewControls"
      >
        <swiper-item wx:for="{{albumImages}}" wx:key="_id">
          <view class="preview-swiper-item">
            <image
              src="{{item.tempFileURL}}"
              mode="aspectFit"
              class="preview-image"
              bindload="onPreviewImageLoad"
              binderror="onPreviewImageError"
            />
          </view>
        </swiper-item>
      </swiper>

      <!-- 底部操作栏 - Android风格 -->
      <view class="preview-bottom-bar {{showPreviewControls ? 'visible' : 'hidden'}}">
        <view class="preview-actions">
          <!-- 收藏按钮 -->
          <view class="preview-action-item" bindtap="onPreviewToggleFavorite">
            <t-icon
              name="{{previewCurrentImage.category === 'favorite' ? 'heart-filled' : 'heart'}}"
              size="28"
              color="{{previewCurrentImage.category === 'favorite' ? '#ff4757' : '#fff'}}"
            />
            <text class="preview-action-label">{{previewCurrentImage.category === 'favorite' ? '已收藏' : '收藏'}}</text>
          </view>

          <!-- 设为首页按钮 -->
          <view class="preview-action-item" bindtap="onPreviewToggleBanner">
            <t-icon
              name="{{previewCurrentImage.bannerOrder ? 'star-filled' : 'star'}}"
              size="28"
              color="{{previewCurrentImage.bannerOrder ? '#ffc107' : '#fff'}}"
            />
            <text class="preview-action-label">{{previewCurrentImage.bannerOrder ? '首页图片' : '设为首页'}}</text>
          </view>

          <!-- 分享按钮 -->
          <view class="preview-action-item" bindtap="onPreviewShare">
            <t-icon name="share" size="28" color="#fff" />
            <text class="preview-action-label">分享</text>
          </view>

          <!-- 删除按钮 -->
          <view class="preview-action-item" bindtap="onPreviewDelete">
            <t-icon name="delete" size="28" color="#e34d59" />
            <text class="preview-action-label">删除</text>
          </view>

          <!-- 更多操作按钮 -->
          <view class="preview-action-item" bindtap="onPreviewMoreActions">
            <t-icon name="ellipsis" size="28" color="#fff" />
            <text class="preview-action-label">更多</text>
          </view>
        </view>
      </view>

      <!-- 加载提示 -->
      <view class="preview-loading-overlay" wx:if="{{previewLoading}}">
        <t-loading theme="circular" size="40px" text="处理中..." />
      </view>
    </view>
  </t-popup>
</view>